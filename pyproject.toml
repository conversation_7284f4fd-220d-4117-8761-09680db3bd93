[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "serv"
version = "0.1.0a4"
description = "Serv is a powerful, extensible, and minimally opinionated ASGI web framework for Python, designed for building modern web applications and APIs with ease."
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "asgiref>=3.8.1",
    "bevy>=3.0.0b6",
    "jinja2>=3.1.6",
    "nubby>=0.1.2",
    "python-multipart>=0.0.20",
    "pyyaml>=6.0.2",
    "tramp>=2025.3.2",
    "werkzeug>=3.1.3",
]

[project.optional-dependencies]
jinja = []
multipart = []

[dependency-groups]
dev = [
    "httpx>=0.28.1",
    "pre-commit>=4.2.0",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.26.0",
    "pytest-mock>=3.14.0",
    "ruff>=0.11.11",
    "uvicorn>=0.34.2",
]

[project.scripts]
serv = "serv.__main__:main"

[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"

[tool.setuptools]
packages = ["serv"]

[tool.uv]
override-dependencies = [
    "tramp>=2025.0.0,<2026.0.0",
    "bevy>=3.0.0b6,<4.0.0",
]
