import sys
import tempfile
from pathlib import Path

import pytest
import yaml
from bevy import dependency
from bevy.registries import Registry

from serv.plugins import Plugin
from serv.plugins.loader import PluginSpec
from serv.responses import ResponseBuilder
from serv.routing import Router
from tests.helpers import create_mock_importer


def create_plugin_with_config(plugin_yaml_content):
    """Helper to create a plugin with specific configuration."""
    temp_dir = tempfile.TemporaryDirectory()
    plugin_dir = Path(temp_dir.name)

    # Create plugin.yaml file
    with open(plugin_dir / "plugin.yaml", "w") as f:
        yaml.dump(plugin_yaml_content, f)

    # Create a dummy __init__.py to make it a package for potential handler imports
    (plugin_dir / "__init__.py").touch()
    sys.path.insert(0, str(temp_dir.name))  # Add temp_dir to path for imports

    class TestPlugin(Plugin):
        # Test handler method
        async def handle_test(self, response: ResponseBuilder = dependency()):
            response.content_type("text/plain")
            response.body("test response")

        # Handler with dependency injection
        async def handle_with_settings(
            self,
            response: ResponseBuilder = dependency(),
            test_setting: str = dependency(),
        ):
            response.content_type("text/plain")
            response.body(f"Setting value: {test_setting}")

        async def on_app_request_begin(self, router: Router = dependency()):
            # Use settings from self.__plugin_spec__ if available, otherwise default
            route_settings_from_spec = (
                getattr(self.__plugin_spec__, "_config", {})
                .get("router", {})
                .get("default_route_settings", {"route_setting": "route_value"})
            )
            test_setting_from_spec = (
                getattr(self.__plugin_spec__, "_config", {})
                .get("router", {})
                .get("default_test_settings", {"test_setting": "injected_value"})
            )

            router.add_route(
                "/test",
                self.handle_test,
                methods=["GET"],
                settings=route_settings_from_spec,
            )
            router.add_route(
                "/test_with_settings",
                self.handle_with_settings,
                methods=["GET"],
                settings=test_setting_from_spec,
            )

    # Patch the module of TestPlugin before instantiation
    test_plugin_module = sys.modules[TestPlugin.__module__]
    original_spec = getattr(test_plugin_module, "__plugin_spec__", None)

    spec_config = {
        "name": plugin_yaml_content.get("name", "Test Plugin Default Name"),
        "description": plugin_yaml_content.get(
            "description", "Test Plugin Default Desc"
        ),
        "version": plugin_yaml_content.get("version", "0.0.0"),
        "author": "Test Author",
    }
    # Include router settings from plugin_yaml_content if they exist, for on_app_request_begin
    if "router" in plugin_yaml_content:
        spec_config["router"] = plugin_yaml_content["router"]

    current_spec = PluginSpec(
        config=spec_config,
        path=plugin_dir,
        override_settings=plugin_yaml_content.get("override_settings", {}),
        importer=create_mock_importer(plugin_dir),
    )
    test_plugin_module.__plugin_spec__ = current_spec

    plugin = TestPlugin(stand_alone=True)
    plugin.__plugin_spec__ = current_spec  # Also set on instance

    # Clean up module patch and sys.path
    if original_spec is not None:
        test_plugin_module.__plugin_spec__ = original_spec
    elif hasattr(test_plugin_module, "__plugin_spec__"):
        del test_plugin_module.__plugin_spec__
    sys.path.pop(0)

    return plugin, temp_dir


@pytest.mark.asyncio
async def test_route_settings():
    """Test route-level settings."""
    plugin_config = {
        "name": "Test Plugin",
        "description": "A test plugin",
        "version": "0.1.0",
    }

    plugin, temp_dir = create_plugin_with_config(plugin_config)
    registry = Registry()
    container = registry.create_container()
    router = Router()
    container.instances[Router] = router

    # Set up routes
    await plugin.on_app_request_begin(router)

    # Resolve a route and check settings
    resolved = router.resolve_route("/test", "GET")
    assert resolved is not None

    handler, params, settings = resolved
    assert settings == {"route_setting": "route_value"}


@pytest.mark.asyncio
async def test_settings_injection():
    """Test that settings are properly injected into handlers."""
    plugin_config = {
        "name": "Test Plugin",
        "description": "A test plugin",
        "version": "0.1.0",
    }

    plugin, temp_dir = create_plugin_with_config(plugin_config)
    registry = Registry()
    container = registry.create_container()
    router = Router()
    container.instances[Router] = router

    # Set up routes
    await plugin.on_app_request_begin(router)

    # Resolve the route
    resolved = router.resolve_route("/test_with_settings", "GET")
    assert resolved is not None

    handler, params, settings = resolved

    # Verify that settings contain the expected value
    assert "test_setting" in settings
    assert settings["test_setting"] == "injected_value"

    # Just verify the key settings structure is correct
    assert isinstance(settings, dict)
    assert len(settings) > 0


@pytest.mark.asyncio
async def test_mounted_router_settings():
    """Test that settings from mounted routers are properly merged."""
    plugin_config = {
        "name": "Test Plugin",
        "description": "A test plugin",
        "version": "0.1.0",
    }

    plugin, temp_dir = create_plugin_with_config(plugin_config)
    registry = Registry()
    container = registry.create_container()
    main_router = Router(
        settings={"main_setting": "main_value", "shared_setting": "main_level"}
    )
    api_router = Router(
        settings={"api_setting": "api_value", "shared_setting": "api_level"}
    )
    container.instances[Router] = main_router

    # Mount the API router
    main_router.mount("/api", api_router)

    # Set up routes on both routers
    await plugin.on_app_request_begin(main_router)
    await plugin.on_app_request_begin(api_router)

    # Resolve a route on the mounted router
    resolved = main_router.resolve_route("/api/test", "GET")
    assert resolved is not None

    handler, params, settings = resolved

    # Check settings inheritance and overriding
    assert settings["main_setting"] == "main_value"
    assert settings["api_setting"] == "api_value"
    assert settings["route_setting"] == "route_value"
    assert settings["shared_setting"] == "api_level"  # Most specific wins
