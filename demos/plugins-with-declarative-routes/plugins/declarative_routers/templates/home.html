{% extends "base.html" %}

{% block title %}Home - Declarative Routes Demo{% endblock %}

{% block content %}
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-xl mb-8">
        <div class="px-6 py-12 sm:px-12 lg:py-16 lg:px-16">
            <div class="text-center">
                <h1 class="text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
                    Welcome to Serv!
                </h1>
                <p class="mt-6 text-xl text-blue-100 max-w-3xl mx-auto">
                    Experience the power of declarative routing with this interactive demo.
                    Built with modern web technologies and designed for developers.
                </p>
                <div class="mt-8 flex justify-center space-x-4">
                    <a href="/dashboard" class="bg-white text-blue-600 hover:bg-blue-50 px-6 py-3 rounded-lg font-semibold transition-colors duration-200 shadow-lg">
                        View Dashboard
                    </a>
                    <a href="/about" class="bg-blue-700 text-white hover:bg-blue-800 px-6 py-3 rounded-lg font-semibold transition-colors duration-200 shadow-lg">
                        Learn More
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Fast Performance</h3>
            <p class="text-gray-600">Lightning-fast routing with minimal overhead and optimized performance.</p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-4">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Easy Configuration</h3>
            <p class="text-gray-600">Simple YAML-based configuration for defining routes and handlers.</p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-4">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Plugin Architecture</h3>
            <p class="text-gray-600">Modular design with powerful plugin system for extensibility.</p>
        </div>
    </div>

    <!-- Getting Started Section -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Getting Started</h2>
        <p class="text-gray-600 mb-6">
            This demo showcases the declarative routing capabilities of the Serv framework.
            Navigate through the different pages to see how routes are configured and handled.
        </p>

        <div class="bg-gray-50 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Available Routes:</h3>
            <ul class="space-y-2">
                <li class="flex items-center">
                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-mono mr-3">/</span>
                    <span class="text-gray-600">Home page with overview and features</span>
                </li>
                <li class="flex items-center">
                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-mono mr-3">/about</span>
                    <span class="text-gray-600">Information about the framework and demo</span>
                </li>
                <li class="flex items-center">
                    <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm font-mono mr-3">/dashboard</span>
                    <span class="text-gray-600">Interactive dashboard with metrics and data</span>
                </li>
            </ul>
        </div>
    </div>
{% endblock %}