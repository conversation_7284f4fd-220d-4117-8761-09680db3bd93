from serv.plugins import Plugin
from serv.app import App
from bevy import dependency
from serv.routing import Router
from serv.responses import ResponseBuilder
import logging

logger = logging.getLogger(__name__)

class {class_name}(Plugin):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._stand_alone = True
    
    async def on_app_request_begin(self, router: Router = dependency()) -> None:
        router.add_route("/hello", self._hello_handler, methods=["GET"])
        
    async def _hello_handler(self, response: ResponseBuilder = dependency()):
        response.content_type("text/plain")
        response.body("Hello from {module_base_name} plugin!")

    def on_load(self) -> None:
        '''Called when the plugin is loaded.
        
        This is a good place to initialize resources or perform setup
        that the plugin needs. You can access plugin-specific configuration
        via `self.config` (which is a dictionary).
        '''
        # Use the main 'serv' logger or get your own as shown above
        logger.info(f"'{{self.name}}' plugin (v{{self.version}}) loaded.")
        # Example: Accessing a configuration value from plugin.yaml's config section
        # example_setting = self.config.get("example_setting", "default_value")
        # logger.info(f"'{{self.name}}' example_setting: {{example_setting}}")

    def on_app_startup(self, app: App) -> None:
        '''Called when the application is starting up.
        
        This method is called once the application instance is created and
        basic setup (like config loading) is done, but before the server
        starts accepting requests. Useful for setting up shared resources
        or adding routes to the application.
        
        Args:
            app: The Serv application instance.
        '''
        # The router configuration in plugin.yaml will be automatically processed
        # but you can also add routes programmatically:
        # 
        # from serv.routing import Router
        # router = app._container.get(Router)
        # router.add_route("/example", {class_name}Route)
        pass

    def on_app_shutdown(self, app: App) -> None:
        '''Called when the application is shutting down.
        
        Useful for cleaning up resources that the plugin might have created
        or maintained during its lifecycle.
        
        Args:
            app: The Serv application instance.
        '''
        logger.info(f"'{{self.name}}' plugin shutting down.")
        pass

    # Example handler methods for routes defined in plugin.yaml
    # These can be referenced by handler_method in the routes configuration
    #
    # from bevy import dependency
    #
    # async def handle_home(self, title: str = dependency()):
    #     """Handle the home route.
    #     
    #     The 'title' parameter will be injected from the settings defined in plugin.yaml
    #     for this route, if present in the 'settings' section.
    #     """
    #     return Template("home.html", {"title": title})
    #
    # async def handle_api(self, db_table: str = dependency(), rate_limit: int = dependency(default=60)):
    #     """Handle the API route.
    #     
    #     Settings from both the router and route are available for injection:
    #     - db_table comes from the route settings
    #     - rate_limit could come from router settings
    #     """
    #     # You could use these settings to customize the handler behavior
    #     return JSONResponse({
    #         "plugin": "{class_name}",
    #         "status": "active",
    #         "db_table": db_table,
    #         "rate_limit": rate_limit
    #     })

    # Other potential lifecycle methods to uncomment and use:
    #
    # def on_app_route_added(self, app: App, path: str, route_handler) -> None:
    #     '''Called after a route has been added to the application's router.'''
    #     pass
    #
    # def on_app_request_end(self, app: App, response, exc: Exception | None) -> None:
    #    '''Called at the end of every request, after a response is generated or an exception occurs.'''
    #    pass

    # You can define other methods specific to your plugin's functionality. 