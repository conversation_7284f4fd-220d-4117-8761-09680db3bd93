name: {plugin_name_human}
display_name: {plugin_name_human}
description: {plugin_description}
version: {plugin_version}
author: {plugin_author}
entry: {plugin_entry_path}

# Settings for the plugin
settings:
  example_setting: "default value"

# Base settings for the plugin
settings:
  # Add your plugin's default settings here
  # example_setting: "default value"

# Define entry points for this plugin (other Plugin subclasses exposed by this plugin)
# Each entry point is loaded along with the main plugin
entry_points:
  # - entry: plugin.submodule:EntryPointClass  # Import path relative to the plugin directory
  #   config:                                  # Optional configuration for this entry point
  #     ep_setting: value

# Define middleware provided by this plugin
middleware:
  # - entry: plugin.middleware:MiddlewareClass  # Import path relative to the plugin directory
  #   config:                                   # Optional configuration for this middleware
  #     mw_setting: value

# Optional: Define a schema for your plugin's configuration.
# This helps with validation and can be used for documentation.
# config_schema:
#   type: object
#   properties:
#     example_setting:
#       type: string
#       description: "An example configuration setting for this plugin."
#       default: "hello"
#   required: [] 

# Optional: Define routers and routes for this plugin.
# These will be automatically set up when the plugin is loaded.
# 
# routers:
#   - name: api_router  # Optional name for reference
#     settings:  # Optional settings for this router
#       auth_required: true
#       rate_limit: 100
#     routes:
#       - path: /users
#         handler: app.routes:UserRoute  # Module:Class format
#         settings:  # Optional settings for this route
#           db_table: users
#           cache_ttl: 300
#       - path: /admin
#         handler: app.routes:admin.AdminRoute  # Module:object.attribute syntax
#         settings:
#           require_role: admin
#       - path: /posts
#         handler_method: handle_posts  # Method on this plugin
#         methods: [GET, POST]          # Optional HTTP methods
#         settings:
#           db_table: posts
#     mount_at: /api    # Optional path to mount this router
#     mount_to: main_router  # Optional name of router to mount to
#
#   - name: main_router
#     settings:
#       template_dir: templates/main
#     routes:
#       - path: /
#         handler: app.routes:HomeRoute
#         settings:
#           title: "Welcome to Our Site"
#       - path: /dashboard
#         handler: app.routes:dashboard.routes.DashboardRoute  # Complex path example
#         settings:
#           widgets: ["stats", "charts", "news"]
#       - path: /about
#         handler_method: handle_about
#         settings:
#           content_file: about.md 